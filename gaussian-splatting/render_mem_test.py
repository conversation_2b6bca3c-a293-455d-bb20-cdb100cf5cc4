#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#

import torch
from scene import Scene
import os
from tqdm import tqdm
from os import makedirs
from gaussian_renderer import render
import torchvision
from utils.general_utils import safe_state
from argparse import ArgumentParser
from arguments import ModelParams, PipelineParams, get_combined_args
from gaussian_renderer import GaussianModel
import numpy as np
try:
    from diff_gaussian_rasterization import SparseGaussianAdam
    SPARSE_ADAM_AVAILABLE = True
except:
    SPARSE_ADAM_AVAILABLE = False

import matplotlib.pyplot as plt
plt.rcParams['font.family'] = 'WenQuanYi Micro Hei'


def render_sets_degree(dataset: ModelParams, iteration: int, pipeline: PipelineParams, skip_train: bool, skip_test: bool, separate_sh: bool):
    dataset.data_device = "cpu"
    torch.cuda.empty_cache()
    
    # 加载场景和高斯模型
    with torch.no_grad():
        
        
        # 测试不同的 mask 比例（k/n）
        sh_degrees = [0, 1, 2, 3]
        peak_memories = []
        
        for degree in sh_degrees:
            gaussians = GaussianModel(degree)
            scene = Scene(dataset, gaussians, load_iteration=iteration, shuffle=False)
            
            print(gaussians._features_rest.shape)
           
            print(f"高斯数量：{gaussians._xyz.shape[0]}")
            
            # 渲染测试集并记录显存峰值
            bg_color = [1, 1, 1] if dataset.white_background else [0, 0, 0]
            background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")
            
            # 清空显存缓存并重置峰值统计
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
            
            # 渲染测试集
            peak_memory = render_set(
                dataset.model_path,
                "test",
                scene.loaded_iter,
                scene.getTestCameras(),
                gaussians,
                pipeline,
                background,
                dataset.train_test_exp,
                separate_sh
            )
            
            peak_memories.append(peak_memory)
            print(f"sh degree: {degree} 显存峰值: {peak_memory:.2f} MB")
            
            torch.cuda.empty_cache()
            
            
        
        # 绘制显存占用曲线
        
        ratios = [(3*(degree+1)**2 + 11) / 59 for degree in sh_degrees]
        plt.figure(figsize=(10, 6))
        plt.plot(ratios, peak_memories, marker='o', linestyle='-', color='b')
        plt.xlabel("sh degree")
        plt.ylabel("显存峰值占用 (MB)")
        plt.title("不同 sh degree 比例下的显存占用")
        plt.grid(True)
        
        # 保存图像
        plot_path = os.path.join(dataset.model_path, "memory_usage_plot_degree.png")
        plt.savefig(plot_path)
        print(f"\n显存占用曲线已保存至: {plot_path}")
        plt.close()
        
def render_sets_ratio(dataset: ModelParams, iteration: int, pipeline: PipelineParams, skip_train: bool, skip_test: bool, separate_sh: bool):
    dataset.data_device = "cpu"
    torch.cuda.empty_cache()
    
    # 加载场景和高斯模型
    with torch.no_grad():
        
        
        # 测试不同的 mask 比例（k/n）
        mask_ratios = [0.15, 0.5, 1.0]  # 10% ~ 100%
        # mask_ratios = [0.13]  # 10% ~ 100%
        peak_memories = []
        
        for ratio in mask_ratios:
            gaussians = GaussianModel(dataset.sh_degree)
            scene = Scene(dataset, gaussians, load_iteration=iteration, shuffle=False)
            n = gaussians._xyz.shape[0]
            k = int(n * ratio)
            # k = 580000
            print(f"\n===== 测试 mask 比例: {ratio*100:.0f}% (k={k}) =====")
            
            # 生成随机 mask（保留前 k 个高斯点）
            mask = torch.zeros(n, dtype=torch.bool, device="cuda")
            mask[:k] = 1

            
            # 深拷贝被 mask 的数据，确保原始数据可以被释放
            gaussians._xyz = gaussians._xyz[mask]
            gaussians._scaling = gaussians._scaling[mask]
            gaussians._features_dc = gaussians._features_dc[mask]
            gaussians._features_rest = gaussians._features_rest[mask]
            gaussians._opacity = gaussians._opacity[mask]
            gaussians._rotation = gaussians._rotation[mask]
            
            print(f"高斯数量：{gaussians._xyz.shape[0]}")
            
            # 渲染测试集并记录显存峰值
            bg_color = [1, 1, 1] if dataset.white_background else [0, 0, 0]
            background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")
            
            # 清空显存缓存并重置峰值统计
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
            
            # 渲染测试集
            peak_memory = render_set(
                dataset.model_path,
                "test",
                scene.loaded_iter,
                scene.getTestCameras(),
                gaussians,
                pipeline,
                background,
                dataset.train_test_exp,
                separate_sh
            )
            
            peak_memories.append(peak_memory)
            print(f"Mask 比例 {ratio*100:.0f}% 显存峰值: {peak_memory:.2f} MB")
            
            torch.cuda.empty_cache()
            
            
        
        # 绘制显存占用曲线
        plt.figure(figsize=(10, 6))
        plt.plot(mask_ratios, peak_memories, marker='o', linestyle='-', color='b')
        plt.xlabel("Mask 比例 (k/n)")
        plt.ylabel("显存峰值占用 (MB)")
        plt.title("不同 Mask 比例下的显存占用")
        plt.grid(True)
        
        # 保存图像
        plot_path = os.path.join(dataset.model_path, "memory_usage_plot.png")
        plt.savefig(plot_path)
        print(f"\n显存占用曲线已保存至: {plot_path}")
        plt.close()

def render_set(model_path, name, iteration, views, gaussians, pipeline, background, train_test_exp, separate_sh):
    render_path = os.path.join(model_path, name, "ours_{}".format(iteration), "renders")
    gts_path = os.path.join(model_path, name, "ours_{}".format(iteration), "gt")

    makedirs(render_path, exist_ok=True)
    makedirs(gts_path, exist_ok=True)
    mems = []
    for idx, view in enumerate(tqdm(views, desc="Rendering progress")):
        torch.cuda.reset_peak_memory_stats()
        view.load_cam_parm_to_device(torch.device("cuda"))
        
        rendering = render(view, gaussians, pipeline, background, use_trained_exp=train_test_exp, separate_sh=separate_sh)["render"]
        # 获取并打印显存峰值
        torch.cuda.synchronize()
        peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)  # 转换为GB
        print(f"显存峰值使用: {peak_memory:.2f} MB")
        mems.append(peak_memory)
        del view
    if mems != []:
        mem_cost = np.array(mems).mean()
        print(f'显存平均消耗：{mem_cost}')
    return mem_cost

def render_sets(dataset : ModelParams, iteration : int, pipeline : PipelineParams, skip_train : bool, skip_test : bool, separate_sh: bool):
    
    dataset.data_device = "cpu"
    torch.cuda.empty_cache()
    torch.cuda.synchronize()
    peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)
    
    with torch.no_grad():
        gaussians = GaussianModel(dataset.sh_degree)
        print(gaussians._xyz.shape[0])
        
        
        scene = Scene(dataset, gaussians, load_iteration=iteration, shuffle=False)
        n = gaussians._xyz.shape[0]
        
        torch.cuda.synchronize()
        peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)  
        print(f"加载gs显存峰值使用: {peak_memory:.2f} MB")
        bg_color = [1,1,1] if dataset.white_background else [0, 0, 0]
        background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")
        
        

        
        render_set(dataset.model_path, "test", scene.loaded_iter, scene.getTestCameras(), gaussians, pipeline, background, dataset.train_test_exp, separate_sh)
    
    

if __name__ == "__main__":
    # Set up command line argument parser
    parser = ArgumentParser(description="Testing script parameters")
    model = ModelParams(parser, sentinel=True)
    pipeline = PipelineParams(parser)
    parser.add_argument("--iteration", default=-1, type=int)
    parser.add_argument("--skip_train", action="store_true")
    parser.add_argument("--skip_test", action="store_true")
    parser.add_argument("--quiet", action="store_true")
    args = get_combined_args(parser)
    print("Rendering " + args.model_path)

    # Initialize system state (RNG)
    safe_state(args.quiet)
   
    render_sets(model.extract(args), args.iteration, pipeline.extract(args), args.skip_train, args.skip_test, SPARSE_ADAM_AVAILABLE)