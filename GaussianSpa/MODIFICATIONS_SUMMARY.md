# 训练脚本修改总结

## 修改的文件

1. `train_sg_imp_score_simul_v4_auto.sh` - 主要训练脚本
2. `render_spherical_gaussian_mem_test_v2_light.py` - 内存测试脚本

## 主要修改内容

### 1. 简化比较逻辑
- **原来**: 复杂的层级比较 (PSNR > SSIM > LPIPS)
- **现在**: 只基于PSNR进行比较
- **代码位置**: `train_sg_imp_score_simul_v4_auto.sh` 第112-123行

### 2. 固定sharpness_threshold参数
- **修改**: 将sharpness_threshold固定为1，移除相关循环
- **影响**: 参数组合从36种减少到12种 (3×4=12)
- **代码位置**: 移除了sharpness_threshold_values数组和相关循环

### 3. 添加结果日志功能
- **新增**: `training_results.txt` 文件记录所有训练结果
- **格式**: `时间戳 | 数据集 | SPA_RATIO1 | SPA_RATIO2 | PSNR | SSIM | LPIPS | Loading Memory | Rendering Memory`
- **特点**: 每次训练结果都追加到同一文件

### 4. 修复指标解析
- **PSNR/SSIM**: 从第3个字段提取 (`awk '{print $3}'`)
- **LPIPS**: 从第2个字段提取 (`awk '{print $2}'`)
- **原因**: metrics.py输出格式为 `  PSNR : 29.2120724`

### 5. 修复内存信息解析
- **Loading Memory**: 从 `Loading peak memory: X MB` 提取第4个字段
- **Rendering Memory**: 修改输出格式为英文 `Rendering peak memory: X MB`
- **修复**: 统一了内存输出格式，避免中英文混合导致的解析错误

### 6. 修复数值比较逻辑
- **问题**: 系统缺少bc命令
- **解决**: 使用awk将浮点数转换为整数进行比较
- **方法**: 乘以1000000后转换为整数，使用bash内置的整数比较

## 输出文件格式

### training_results.txt 示例
```
Training Results Log - Started at 2025-08-17 10:00:00
Format: Timestamp | Dataset | SPA_RATIO1 | SPA_RATIO2 | PSNR | SSIM | LPIPS | Loading Memory | Rendering Memory
==================================================================================================================================================
2025-08-17 10:30:00 | Dataset: train | SPA_RATIO1: 0.4 | SPA_RATIO2: 0.65 | PSNR: 29.2120724 | SSIM: 0.9172814 | LPIPS: 0.1929932 | Loading Memory: 66.052734375MB | Rendering Memory: 45.123456MB
...
==================================================================================================================================================
SUMMARY - Completed at 2025-08-17 15:00:00
Best results found:
Best PSNR: 29.5
Best SSIM: 0.92
Best LPIPS: 0.18
Corresponding parameters: SPA_RATIO1=0.45, SPA_RATIO2=0.7, sharpness_threshold=1
```

## 参数范围
- **SPA_RATIO1**: [0.4, 0.45, 0.5, 0.55, 0.6]
- **SPA_RATIO2**: [0.65, 0.68, 0.7, 0.72, 0.75, 0.78, 0.8]
- **sharpness_threshold**: 固定为1

## 测试脚本
- `test_parsing.sh` - 测试基本解析逻辑
- `test_full_parsing.sh` - 测试完整解析流程
- `test_memory_script.py` - 测试内存脚本导入

## 使用方法
1. 运行修改后的脚本: `./train_sg_imp_score_simul_v4_auto.sh`
2. 查看实时结果: `tail -f training_results.txt`
3. 脚本完成后查看最佳结果总结

## 注意事项
1. 确保所有Python依赖已安装
2. 确保有足够的GPU内存
3. 结果文件会在当前目录生成
4. 每次运行会覆盖之前的结果文件
