import torch
from scene import Scene
# from scene.spherical_gaussian_model import SphericalGaussianModel
from scene.spherical_gaussian_model_cullSG_v5 import SphericalGaussianModelcullSGv5
# from scene.spherical_gaussian_model_cullSG_v5_energy import SphericalGaussianModelcullSGv5

# from spherical_gaussian_renderer import render
from spherical_gaussian_renderer_v2_light import render,render_imp
import os
from tqdm import tqdm
from os import makedirs
from argparse import ArgumentParser
from arguments import ModelParams, PipelineParams, get_combined_args
from utils.general_utils import safe_state
import torchvision
import numpy as np

def render_set(model_path, name, iteration, views, gaussians, pipeline, background):
    mems = []
    torch.cuda.reset_peak_memory_stats()
    for idx, view in enumerate(tqdm(views, desc="Rendering progress")):
        with torch.no_grad():
            with torch.cuda.amp.autocast():
                view.load_cam_parm_to_device(torch.device("cuda"))
                # rendering = render(view, gaussians, pipeline, background)["render"]
                # v2
                rendering = render_imp(view, gaussians, pipeline, background, is_training=False)["render"]
    torch.cuda.synchronize()
    peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)  # 转换为MB
    # print(f"显存峰值使用: {peak_memory:.2f} MB")
    mems.append(peak_memory)
    del view
    if mems != []:
        rendering_memory = np.array(mems).mean()
        print(f'Rendering peak memory: {rendering_memory} MB')
        return rendering_memory
    return 0

def render_sets(dataset: ModelParams, iteration: int, pipeline: PipelineParams, skip_train: bool, skip_test: bool):

    with torch.no_grad():
        dataset.data_device = "cpu"

        # Measure loading memory
        torch.cuda.reset_peak_memory_stats()
        gaussians = SphericalGaussianModelcullSGv5(dataset.sg_degree)
        scene = Scene(dataset, gaussians, load_iteration=iteration, shuffle=False)
        torch.cuda.synchronize()
        loading_peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)  # 转换为MB

        print(f"Loading peak memory: {loading_peak_memory} MB")

        bg_color = [1,1,1] if dataset.white_background else [0, 0, 0]
        background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")


        rendering_memory = render_set(dataset.model_path, "train", scene.loaded_iter, scene.getTestCameras(), gaussians, pipeline, background)

        return loading_peak_memory, rendering_memory

        

if __name__ == "__main__":
    # 设置命令行参数
    parser = ArgumentParser(description="Testing script parameters")
    model = ModelParams(parser, sentinel=True)
    pipeline = PipelineParams(parser)
    parser.add_argument("--iteration", default=-1, type=int)
    parser.add_argument("--skip_train", action="store_true")
    parser.add_argument("--skip_test", action="store_true")
    parser.add_argument("--quiet", action="store_true")
    args = get_combined_args(parser)
    print("Rendering " + args.model_path)

    # 初始化系统状态
    safe_state(args.quiet)

    render_sets(model.extract(args), args.iteration, pipeline.extract(args), args.skip_train, args.skip_test)
