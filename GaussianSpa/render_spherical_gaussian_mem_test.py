import torch
from scene import Scene
from scene.spherical_gaussian_model import SphericalGaussianModel
# from scene.spherical_gaussian_model_cullSG_new import SphericalGaussianModelcullSGnew
# from scene.spherical_gaussian_model_cullSG_v2 import SphericalGaussianModelcullSGv2
# from scene.gaussian_model import GaussianModel
# from scene.spherical_gaussian_model_cullSG import SphericalGaussianModelcullSG
from spherical_gaussian_renderer import render_imp
# from gaussian_renderer import render_imp
import os
from tqdm import tqdm
from os import makedirs
from argparse import ArgumentParser
from arguments import ModelParams, PipelineParams, get_combined_args
from utils.general_utils import safe_state
import torchvision
import numpy as np

def render_set(model_path, name, iteration, views, gaussians, pipeline, background):
    mems = []
    torch.cuda.reset_peak_memory_stats()
    for idx, view in enumerate(tqdm(views, desc="Rendering progress")):
        with torch.no_grad():
            with torch.cuda.amp.autocast():
                view.load_cam_parm_to_device(torch.device("cuda"))
                rendering = render_imp(view, gaussians, pipeline, background)["render"]
                
    torch.cuda.synchronize()
    peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)  # 转换为GB
        # print(f"显存峰值使用: {peak_memory:.2f} MB")
    mems.append(peak_memory)
    del view
    if mems != []:
        print(f'显存平均消耗：{np.array(mems).mean()}')

def render_sets(dataset: ModelParams, iteration: int, pipeline: PipelineParams, skip_train: bool, skip_test: bool):
    
    with torch.no_grad():
        dataset.data_device = "cpu"
        gaussians = SphericalGaussianModel(dataset.sh_degree)
        scene = Scene(dataset, gaussians, load_iteration=iteration, shuffle=False)

        bg_color = [1,1,1] if dataset.white_background else [0, 0, 0]
        background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")

        
        render_set(dataset.model_path, "train", scene.loaded_iter, scene.getTestCameras(), gaussians, pipeline, background)

        

if __name__ == "__main__":
    # 设置命令行参数
    parser = ArgumentParser(description="Testing script parameters")
    model = ModelParams(parser, sentinel=True)
    pipeline = PipelineParams(parser)
    parser.add_argument("--iteration", default=-1, type=int)
    parser.add_argument("--skip_train", action="store_true")
    parser.add_argument("--skip_test", action="store_true")
    parser.add_argument("--quiet", action="store_true")
    args = get_combined_args(parser)
    print("Rendering " + args.model_path)

    # 初始化系统状态
    safe_state(args.quiet)

    render_sets(model.extract(args), args.iteration, pipeline.extract(args), args.skip_train, args.skip_test)
