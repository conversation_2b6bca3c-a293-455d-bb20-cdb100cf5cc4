#!/bin/bash

# Test script to verify the parsing logic

echo "Testing metrics parsing..."

# Simulate metrics output
METRICS_OUTPUT="Method: ours_40000
  SSIM : 0.9172814
  PSNR : 29.2120724
  LPIPS: 0.1929932

"

echo "Simulated METRICS_OUTPUT:"
echo "$METRICS_OUTPUT"
echo ""

# Test extraction
current_ssim=$(echo "$METRICS_OUTPUT" | grep "SSIM" | awk '{print $3}')
current_psnr=$(echo "$METRICS_OUTPUT" | grep "PSNR" | awk '{print $3}')
current_lpips=$(echo "$METRICS_OUTPUT" | grep "LPIPS" | awk '{print $2}')

echo "Extracted metrics:"
echo "SSIM: '$current_ssim'"
echo "PSNR: '$current_psnr'"
echo "LPIPS: '$current_lpips'"
echo ""

echo "Testing memory parsing..."

# Simulate memory output
MEM_OUTPUT="Rendering /ghome/l3/yliu3/chenyikeng/GaussianSpa/simul_v4_energy_di/train/3_15000for0.4_25200-35200for0.65_1_40000
Loading peak memory: 66.052734375 MB
Rendering progress: 100%|██████████| 352/352 [00:32<00:00, 10.73it/s]
Rendering peak memory: 45.123456 MB
"

echo "Simulated MEM_OUTPUT:"
echo "$MEM_OUTPUT"
echo ""

# Test extraction
loading_memory=$(echo "$MEM_OUTPUT" | grep "Loading peak memory:" | awk '{print $4}')
rendering_memory=$(echo "$MEM_OUTPUT" | grep "Rendering peak memory:" | awk '{print $4}')

echo "Extracted memory:"
echo "Loading memory: '$loading_memory'"
echo "Rendering memory: '$rendering_memory'"
echo ""

# Test final log format
DATASET_NAME="train"
SPA_RATIO1="0.4"
SPA_RATIO2="0.65"

echo "Final log entry would be:"
echo "$(date '+%Y-%m-%d %H:%M:%S') | Dataset: $DATASET_NAME | SPA_RATIO1: $SPA_RATIO1 | SPA_RATIO2: $SPA_RATIO2 | PSNR: $current_psnr | SSIM: $current_ssim | LPIPS: $current_lpips | Loading Memory: ${loading_memory}MB | Rendering Memory: ${rendering_memory}MB"
