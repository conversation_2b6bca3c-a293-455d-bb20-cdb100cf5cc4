#!/bin/bash

# Test the full parsing logic with realistic outputs

echo "=== Testing Full Parsing Logic ==="

# Simulate realistic metrics output (from actual metrics.py)
METRICS_OUTPUT="Method: ours_40000
Metric evaluation progress: 100%|██████████| 352/352 [00:15<00:00, 22.45it/s]
  SSIM : 0.9172814
  PSNR : 29.2120724
  LPIPS: 0.1929932

"

echo "Simulated METRICS_OUTPUT:"
echo "$METRICS_OUTPUT"
echo ""

# Extract metrics using the same logic as the main script
current_ssim=$(echo "$METRICS_OUTPUT" | grep "SSIM" | awk '{print $3}')
current_psnr=$(echo "$METRICS_OUTPUT" | grep "PSNR" | awk '{print $3}')
current_lpips=$(echo "$METRICS_OUTPUT" | grep "LPIPS" | awk '{print $2}')

echo "Extracted metrics:"
echo "SSIM: '$current_ssim'"
echo "PSNR: '$current_psnr'"
echo "LPIPS: '$current_lpips'"
echo ""

# Simulate realistic memory output
MEM_OUTPUT="Rendering /ghome/l3/yliu3/chenyikeng/GaussianSpa/simul_v4_energy_di/train/3_15000for0.4_25200-35200for0.65_1_40000
Loading peak memory: 66.052734375 MB
Rendering progress: 100%|██████████| 352/352 [00:32<00:00, 10.73it/s]
Rendering peak memory: 45.123456 MB
"

echo "Simulated MEM_OUTPUT:"
echo "$MEM_OUTPUT"
echo ""

# Extract memory using the same logic as the main script
loading_memory=$(echo "$MEM_OUTPUT" | grep "Loading peak memory:" | awk '{print $4}')
rendering_memory=$(echo "$MEM_OUTPUT" | grep "Rendering peak memory:" | awk '{print $4}')

# Apply the same default value logic
if [ -z "$loading_memory" ]; then
    loading_memory="N/A"
fi
if [ -z "$rendering_memory" ]; then
    rendering_memory="N/A"
fi

echo "Extracted memory:"
echo "Loading memory: '$loading_memory'"
echo "Rendering memory: '$rendering_memory'"
echo ""

# Test the final log entry format
DATASET_NAME="train"
SPA_RATIO1="0.4"
SPA_RATIO2="0.65"

echo "Final log entry:"
echo "$(date '+%Y-%m-%d %H:%M:%S') | Dataset: $DATASET_NAME | SPA_RATIO1: $SPA_RATIO1 | SPA_RATIO2: $SPA_RATIO2 | PSNR: $current_psnr | SSIM: $current_ssim | LPIPS: $current_lpips | Loading Memory: ${loading_memory}MB | Rendering Memory: ${rendering_memory}MB"
echo ""

# Test PSNR comparison logic
best_psnr=-1
echo "Testing PSNR comparison:"
echo "Current PSNR: $current_psnr"
echo "Best PSNR: $best_psnr"

# Convert to integer comparison by multiplying by 1000000 to handle decimals
current_psnr_int=$(echo "$current_psnr * 1000000" | awk '{printf "%.0f", $1}')
best_psnr_int=$(echo "$best_psnr * 1000000" | awk '{printf "%.0f", $1}')

echo "Current PSNR (int): $current_psnr_int"
echo "Best PSNR (int): $best_psnr_int"

if [ "$current_psnr_int" -gt "$best_psnr_int" ] 2>/dev/null; then
    echo "✓ Current PSNR is better than best PSNR - would update best results"
    best_psnr=$current_psnr
    best_ssim=$current_ssim
    best_lpips=$current_lpips
    echo "New best PSNR: $best_psnr"
else
    echo "✗ Current PSNR is not better than best PSNR - would not update"
fi

echo ""
echo "=== Test Complete ==="
